:root {
    --primary-color: #c8a97e;
    --secondary-color: #4a4a4a;
    --light-color: #f8f9fa;
    --dark-color: #333;
    --danger-color: #dc3545;
    --success-color: #28a745;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: #f5f5f5;
}

/* Login Page Styles */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-image: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url('../images/header_img.jpg');
    background-size: cover;
    background-position: center;
}

.login-box {
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 400px;
}

.logo {
    text-align: center;
    margin-bottom: 20px;
}

.logo h1 {
    color: var(--primary-color);
    font-size: 1.8rem;
}

.input-group {
    margin-bottom: 20px;
}

.input-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.input-group input, 
.input-group textarea,
.input-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.error-message {
    color: var(--danger-color);
    margin-bottom: 15px;
    font-size: 0.9rem;
    min-height: 20px;
}

.login-btn, 
.save-changes-btn,
.save-settings-btn,
.save-item-btn,
.add-item-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    width: 100%;
    transition: background 0.3s;
}

.login-btn:hover,
.save-changes-btn:hover,
.save-settings-btn:hover,
.save-item-btn:hover,
.add-item-btn:hover {
    background-color: #b69a6b;
}

.back-link {
    display: block;
    text-align: center;
    margin-top: 20px;
    color: var(--secondary-color);
    text-decoration: none;
}

.back-link:hover {
    color: var(--primary-color);
}

/* Dashboard Styles */
.admin-container {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    width: 250px;
    background-color: var(--dark-color);
    color: white;
    padding: 20px 0;
    display: flex;
    flex-direction: column;
}

.admin-nav ul {
    list-style: none;
    margin-top: 30px;
}

.admin-nav li {
    padding: 15px 20px;
    cursor: pointer;
    transition: background 0.3s;
}

.admin-nav li:hover, .admin-nav li.active {
    background-color: var(--primary-color);
}

.admin-nav li i {
    margin-right: 10px;
}

.logout {
    margin-top: auto;
    padding: 20px;
}

#logout-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 1rem;
    display: flex;
    align-items: center;
}

#logout-btn i {
    margin-right: 10px;
}

.content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.tab-content h2 {
    margin-bottom: 30px;
    color: var(--primary-color);
}

.category-selector {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.category-selector label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: var(--primary-color);
}

.menu-items-editor {
    margin-bottom: 30px;
}

.menu-item-card {
    display: flex;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    margin-bottom: 15px;
    overflow: hidden;
}

.menu-item-image {
    width: 120px;
    height: 120px;
    overflow: hidden;
    flex-shrink: 0;
}

.menu-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.menu-item-info {
    flex: 1;
    padding: 15px;
}

.menu-item-info h3 {
    color: var(--primary-color);
    margin-bottom: 5px;
}

.menu-item-actions {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 15px;
    gap: 10px;
}

.edit-btn, .delete-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.2rem;
}

.edit-btn {
    color: var(--primary-color);
}

.delete-btn {
    color: var(--danger-color);
}

.add-item-btn, .save-changes-btn {
    margin-right: 10px;
    width: auto;
}

/* Image Manager */
.image-uploader {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 30px;
}

.image-gallery {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    padding: 20px;
}

.images-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.image-item {
    position: relative;
    height: 150px;
    border-radius: 4px;
    overflow: hidden;
}

.image-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-item .delete-image {
    position: absolute;
    top: 5px;
    right: 5px;
    background-color: rgba(220, 53, 69, 0.8);
    color: white;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    position: relative;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.modal-header h2 {
    margin: 0;
    color: var(--primary-color);
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--secondary-color);
    transition: color 0.3s;
}

.close-modal:hover {
    color: var(--danger-color);
}

/* Enhanced Dropdown Styling */
select {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23c8a97e' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 16px;
    padding-right: 40px !important;
    cursor: pointer;
}

select:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(200, 169, 126, 0.25);
}

/* File Input Styling */
.file-input-container {
    position: relative;
    margin-bottom: 20px;
}

.file-input-container input[type="file"] {
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-input-trigger {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    background-color: var(--light-color);
    border: 2px dashed var(--primary-color);
    border-radius: 4px;
    color: var(--secondary-color);
    font-weight: 500;
    transition: all 0.3s;
}

.file-input-trigger:hover {
    background-color: rgba(200, 169, 126, 0.1);
}

.file-input-trigger i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.file-name {
    margin-top: 10px;
    font-size: 0.9rem;
    color: var(--secondary-color);
}

/* Upload Button */
.upload-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background 0.3s;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.upload-btn:hover {
    background-color: #b69a6b;
}

.upload-btn i {
    font-size: 1.2rem;
}

/* Button Group */
.button-group {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.button-group button {
    flex: 1;
}

/* Image Preview */
.image-preview {
    margin-top: 15px;
    display: none;
    text-align: center;
    position: relative;
}

.image-preview img {
    max-width: 100%;
    max-height: 200px;
    border-radius: 8px;
    border: 1px solid #ddd;
}

.remove-image-btn {
    margin-top: 10px;
    background-color: var(--danger-color);
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
}

.remove-image-btn:hover {
    background-color: #bd2130;
}

/* Form Section Dividers */
.form-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.form-section-title {
    font-size: 1.1rem;
    color: var(--primary-color);
    margin-bottom: 15px;
    font-weight: 600;
}

/* Success Message */
.success-message {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    display: none;
}

.success-message.show {
    display: block;
    animation: fadeOut 3s forwards;
    animation-delay: 2s;
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.empty-state i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 15px;
    opacity: 0.5;
}

.empty-state p {
    color: var(--secondary-color);
    font-size: 1.1rem;
}

/* Responsive */
@media (max-width: 768px) {
    .admin-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        padding: 10px 0;
    }
    
    .admin-nav ul {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
    }
    
    .admin-nav li {
        width: 50%;
        padding: 10px 15px;
    }
    
    .content {
        padding: 20px;
    }
    
    .tab-content h2 {
        font-size: 1.5rem;
    }
    
    .menu-item-card {
        flex-direction: column;
    }
    
    .menu-item-info {
        margin-bottom: 10px;
    }
    
    .menu-item-actions {
        flex-direction: column;
        gap: 10px;
    }
    
    .add-item-btn, .save-changes-btn {
        width: 100%;
    }
    
    .image-uploader, .image-gallery {
        padding: 15px;
    }
    
    .images-container {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
    
    .image-item {
        height: 100px;
    }
}

/* Language Display */
.ar {
    display: none !important;
    direction: rtl;
    font-family: 'Arial', sans-serif;
}

html[lang="ar"] .ar {
    display: block !important;
}

html[lang="ar"] .en {
    display: none !important;
}

/* Language Switcher */
.language-switcher {
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 1000;
    display: flex;
    gap: 5px;
}

.language-switcher button {
    background-color: var(--light-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s;
}

.language-switcher button.active {
    background-color: var(--primary-color);
    color: white;
}

/* RTL Support */
html[lang="ar"] .sidebar {
    left: auto;
    right: 0;
}

html[lang="ar"] .content {
    margin-left: 0;
    margin-right: 250px;
}

html[lang="ar"] .admin-nav li {
    text-align: right;
}

html[lang="ar"] .form-section-title,
html[lang="ar"] label,
html[lang="ar"] input,
html[lang="ar"] textarea,
html[lang="ar"] select,
html[lang="ar"] .menu-item-info {
    text-align: right;
}

html[lang="ar"] .menu-item-card {
    flex-direction: row-reverse;
}

html[lang="ar"] .menu-item-actions {
    flex-direction: column;
}

@media (max-width: 768px) {
    html[lang="ar"] .content {
        margin-right: 0;
    }
}







