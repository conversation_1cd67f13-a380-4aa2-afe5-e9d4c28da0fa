<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login | Al-Andalus Restaurant</title>
    <link rel="icon" href="../images/favicon.png" type="image/png">
    <link rel="stylesheet" href="admin-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="login-container">
        <div class="login-box">
            <div class="logo">
                <h1>Al-Andalus Admin</h1>
            </div>
            <form id="login-form">
                <div class="input-group">
                    <label for="password">Enter admin password to continue</label>
                    <input type="password" id="password" required>
                </div>
                <div class="error-message" id="error-message"></div>
                <button type="submit" class="login-btn">Login</button>
            </form>
            <a href="../index.html" class="back-link">
                <i class="fas fa-arrow-left"></i> Back to Website
            </a>
        </div>
    </div>
    <script type="module">
  // Import the functions you need from the SDKs you need
  import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
  // TODO: Add SDKs for Firebase products that you want to use
  // https://firebase.google.com/docs/web/setup#available-libraries

  // Your web app's Firebase configuration
  const firebaseConfig = {
    apiKey: "AIzaSyB-LGT0oIM6Q4S496tsqRgUdY7atU0-aEA",
    authDomain: "restaurant-61a71.firebaseapp.com",
    projectId: "restaurant-61a71",
    storageBucket: "restaurant-61a71.firebasestorage.app",
    messagingSenderId: "797821083486",
    appId: "1:797821083486:web:9e625179dc84c5179f79cf"
  };

  // Initialize Firebase
  const app = initializeApp(firebaseConfig);
</script>
    <script src="admin-login.js"></script>
</body>
</html>
