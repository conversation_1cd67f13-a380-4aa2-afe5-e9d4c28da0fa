:root {
    --primary-color: #c8a97e;
    --secondary-color: #4a4a4a;
    --light-color: #f8f9fa;
    --dark-color: #333;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Language Switcher */
.language-switcher {
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 1000;
}

/* Admin Login Button */
.admin-login-btn {
    position: fixed;
    top: 10px;
    left: 10px;
    z-index: 1000;
}

.admin-login-btn button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.admin-login-btn button:hover {
    background-color: #b69a6b;
    transform: translateY(-1px);
}

.admin-login-btn button i {
    font-size: 1rem;
}

.language-switcher button {
    padding: 5px 10px;
    background: var(--light-color);
    border: 1px solid var(--primary-color);
    cursor: pointer;
}

.language-switcher button.active {
    background: var(--primary-color);
    color: white;
}

/* Language Display */
.ar {
    display: none !important;
    direction: rtl;
    font-family: 'Arial', sans-serif;
}

html[lang="ar"] .ar {
    display: block !important;
}

html[lang="ar"] .en {
    display: none !important;
}

/* Fix for Arabic menu items */
html[lang="ar"] .menu-item-info h3,
html[lang="ar"] .menu-item-info p,
html[lang="ar"] .menu-item-info .price {
    display: block;
    margin-bottom: 10px;
    text-align: right;
}

html[lang="ar"] .menu-item-info {
    display: flex;
    flex-direction: column;
}

html[lang="ar"] .price {
    margin-top: auto;
}

/* Button Containers */
.button-container, .map-btn-container {
    display: flex;
    justify-content: center;
}

.btn, .map-btn {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 5px;
    transition: background 0.3s;
    text-align: center;
    min-width: 150px;
    max-width: 200px;
}

.btn:hover, .map-btn:hover {
    background: #b69a6b;
}

/* Header */
header {
    background-color: var(--light-color);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 15px;
}

.logo h1 {
    color: var(--primary-color);
    font-size: 1.8rem;
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 20px;
}

nav ul li a {
    text-decoration: none;
    color: var(--secondary-color);
    font-weight: 500;
    transition: color 0.3s;
}

nav ul li a:hover {
    color: var(--primary-color);
}

/* Background Sections */
.bg-section {
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    color: white;
    position: relative;
}

.bg-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 1;
}

.bg-section .container {
    position: relative;
    z-index: 2;
}

/* Hero Section with Background */
#hero {
    background-image: url('images/header_img.jpg');
    background-size: cover;
    background-position: center;
    color: white;
    text-align: center;
    padding: 100px 0;
    position: relative;
}

#hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

#hero .container {
    position: relative;
}

#hero h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

/* Image Slider Improvements */
#image-slider {
    padding: 50px 0;
    background-color: var(--light-color);
}

.slider-container {
    width: 100%;
    overflow: hidden;
    padding: 20px 0;
}

.slider {
    display: flex;
    transition: transform 0.5s ease;
}

.slide {
    min-width: 200px;
    height: 150px;
    margin: 0 5px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Menu Section */
#menu {
    padding: 80px 0;
    background-color: white;
}

.menu-categories {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    margin: 30px 0;
    gap: 10px;
}

.menu-tab {
    padding: 10px 15px;
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s;
    white-space: nowrap;
}

.menu-tab.active, .menu-tab:hover {
    background: var(--primary-color);
    color: white;
}

.menu-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.menu-item {
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.menu-item:hover {
    transform: translateY(-5px);
}

.menu-item-info {
    padding: 20px;
    background: white;
}

.menu-item-info h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

.menu-item-info p {
    color: var(--secondary-color);
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.price {
    font-weight: bold;
    color: var(--dark-color);
    font-size: 1.1rem;
}

.menu-item-image {
    height: 200px;
    background-size: cover;
    background-position: center;
}

/* About Section */
#about {
    padding: 80px 0;
    background-image: url('images/about.jpg');
}

.about-content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 40px;
    margin-top: 30px;
}

.about-text {
    flex: 1;
    min-width: 300px;
}

.about-text p {
    margin-bottom: 20px;
    line-height: 1.8;
}

.about-image {
    flex: 1;
    min-width: 300px;
    height: 350px;
    background-size: cover;
    background-position: center;
    border-radius: 8px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
}

/* Contact Section */
#contact {
    background-color: var(--light-color);
    padding: 50px 0;
    text-align: center;
}

.contact-info {
    margin-top: 30px;
}

.contact-info div {
    margin-bottom: 20px;
}

.contact-info i {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.map-btn {
    min-width: 225px;
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 12px 25px;
    text-decoration: none;
    border-radius: 5px;
    margin-top: 20px;
    transition: background 0.3s;
}

.map-btn:hover {
    background: #b69a6b;
}

/* Footer */
footer {
    background-color: var(--dark-color);
    color: white;
    text-align: center;
    padding: 20px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    header .container {
        flex-direction: column;
    }
    
    nav ul {
        margin-top: 20px;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    nav ul li {
        margin: 5px 10px;
    }
    
    #hero h2 {
        font-size: 2rem;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .menu-item {
        flex-direction: column;
    }
    
    .about-content {
        flex-direction: column;
    }
    
    .about-image {
        width: 100%;
        margin-top: 20px;
    }
    
    .slide {
        min-width: 200px;
        height: 150px;
    }
}

/* Food Gallery */
.food-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.food-item {
    height: 200px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.food-item:hover {
    transform: translateY(-5px);
}

.food-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .food-gallery {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
    
    .food-item {
        height: 150px;
    }
}

/* Responsive Menu Tabs */
@media (max-width: 768px) {
    .menu-tab {
        padding: 8px 12px;
        font-size: 0.9rem;
    }
}

/* Admin link */
.admin-link {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: rgba(200, 169, 126, 0.2);
    color: var(--primary-color);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s;
}

.admin-link:hover {
    background-color: var(--primary-color);
    color: white;
}

.admin-link button {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    padding: 8px 12px;
    border-radius: 4px;
    transition: all 0.3s;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 2000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    width: 90%;
    max-width: 400px;
    position: relative;
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.modal-header h2 {
    margin: 0;
    color: var(--primary-color);
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--secondary-color);
    transition: color 0.3s;
}

.close-modal:hover {
    color: var(--primary-color);
}

.input-group {
    margin-bottom: 20px;
}

.input-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--dark-color);
}

.input-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.input-group input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(200, 169, 126, 0.25);
}

.error-message {
    color: #dc3545;
    margin-bottom: 15px;
    font-size: 0.9rem;
    min-height: 20px;
}

.login-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    width: 100%;
    transition: background 0.3s;
}

.login-btn:hover {
    background-color: #b69a6b;
}

