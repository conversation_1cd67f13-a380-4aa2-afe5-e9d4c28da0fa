<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard | Al-Andalus Restaurant</title>
    <link rel="icon" href="../images/favicon.png" type="image/png">
    <link rel="stylesheet" href="admin-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="language-switcher">
        <button onclick="switchLanguage('en')" id="en-btn">English</button>
        <button onclick="switchLanguage('ar')" class="active" id="ar-btn">العربية</button>
    </div>

    <div class="admin-container">
        <aside class="sidebar">
            <div class="logo">
                <h2 class="en">Al-Andalus Admin</h2>
                <h2 class="ar">إدارة الأندلس</h2>
            </div>
            <nav class="admin-nav">
                <ul>
                    <li class="active" data-tab="menu-editor"><i class="fas fa-utensils"></i> <span class="en">Menu Editor</span><span class="ar">محرر القائمة</span></li>
                    <li data-tab="settings"><i class="fas fa-cog"></i> <span class="en">Settings</span><span class="ar">الإعدادات</span></li>
                </ul>
            </nav>
            <button id="logout-btn"><i class="fas fa-sign-out-alt"></i> <span class="en">Logout</span><span class="ar">تسجيل الخروج</span></button>
        </aside>

        <main class="content">
            <div class="success-message" id="success-message"></div>

            <div class="tab-content active" id="menu-editor">
                <h2 class="en">Menu Editor</h2>
                <h2 class="ar">محرر القائمة</h2>
                <div class="category-selector">
                    <label for="category-select" class="en">Select Menu Category:</label>
                    <label for="category-select" class="ar">اختر فئة القائمة:</label>
                    <select id="category-select">
                        <option value="appetizers" class="en">Appetizers</option>
                        <option value="appetizers" class="ar">المقبلات</option>
                        <option value="main" class="en">Main Courses</option>
                        <option value="main" class="ar">الأطباق الرئيسية</option>
                        <option value="seafood" class="en">Seafood</option>
                        <option value="seafood" class="ar">المأكولات البحرية</option>
                        <option value="salads" class="en">Salads</option>
                        <option value="salads" class="ar">السلطات</option>
                        <option value="soups" class="en">Soups</option>
                        <option value="soups" class="ar">الشوربات</option>
                        <option value="sandwiches" class="en">Sandwiches</option>
                        <option value="sandwiches" class="ar">السندويشات</option>
                        <option value="desserts" class="en">Desserts</option>
                        <option value="desserts" class="ar">الحلويات</option>
                        <option value="beverages" class="en">Beverages</option>
                        <option value="beverages" class="ar">المشروبات</option>
                        <option value="breakfast" class="en">Breakfast</option>
                        <option value="breakfast" class="ar">الإفطار</option>
                        <option value="specials" class="en">Chef's Specials</option>
                        <option value="specials" class="ar">أطباق الشيف الخاصة</option>
                    </select>
                </div>

                <div class="menu-items-editor">
                    <!-- Menu items will be loaded here dynamically -->
                </div>

                <div class="button-group">
                    <button class="add-item-btn">
                        <i class="fas fa-plus"></i>
                        <span class="en">Add New Item</span>
                        <span class="ar">إضافة عنصر جديد</span>
                    </button>
                    <button class="save-changes-btn">
                        <i class="fas fa-save"></i>
                        <span class="en">Save Changes</span>
                        <span class="ar">حفظ التغييرات</span>
                    </button>
                </div>
            </div>

            <div class="tab-content" id="image-manager">
                <h2>Image Manager</h2>
                <div class="image-uploader">
                    <h3>Upload New Image</h3>
                    <form id="image-upload-form">
                        <div class="file-input-container">
                            <div class="file-input-trigger">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <span>Click or drag to upload image</span>
                            </div>
                            <input type="file" id="image-file" accept="image/*">
                        </div>
                        <div class="file-name" id="file-name">No file selected</div>
                        <div class="image-preview" id="image-preview">
                            <img id="preview-img" src="" alt="Preview">
                        </div>
                        <button type="submit" class="upload-btn"><i class="fas fa-upload"></i> Upload Image</button>
                    </form>
                </div>

                <div class="image-gallery">
                    <h3>Image Gallery</h3>
                    <div class="images-container">
                        <!-- Images will be loaded here dynamically -->
                    </div>
                </div>
            </div>

            <div class="tab-content" id="settings">
                <h2>Settings</h2>
                <form id="settings-form">
                    <div class="form-section">
                        <div class="form-section-title">Restaurant Information</div>
                        <div class="input-group">
                            <label for="restaurant-name-en">Restaurant Name (English)</label>
                            <input type="text" id="restaurant-name-en" value="Al-Andalus Restaurant">
                        </div>

                        <div class="input-group">
                            <label for="restaurant-name-ar">Restaurant Name (Arabic)</label>
                            <input type="text" id="restaurant-name-ar" value="مطعم الأندلس">
                        </div>
                    </div>

                    <div class="form-section">
                        <div class="form-section-title">Contact Information</div>
                        <div class="input-group">
                            <label for="contact-phone">Contact Phone</label>
                            <input type="text" id="contact-phone" value="+966 12 534 0000">
                        </div>

                        <div class="input-group">
                            <label for="contact-address-en">Address (English)</label>
                            <input type="text" id="contact-address-en" value="Ibrahim El Khalil Street, Al Haram, Makkah 21955, Saudi Arabia">
                        </div>

                        <div class="input-group">
                            <label for="contact-address-ar">Address (Arabic)</label>
                            <input type="text" id="contact-address-ar" value="شارع إبراهيم الخليل، الحرم، مكة 21955، المملكة العربية السعودية">
                        </div>
                    </div>

                    <button type="submit" class="save-settings-btn"><i class="fas fa-save"></i> Save Settings</button>
                </form>
            </div>
        </main>
    </div>

    <!-- Item Editor Modal -->
    <div class="modal" id="item-editor-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title" class="en">Edit Menu Item</h2>
                <h2 id="modal-title-ar" class="ar">تعديل عنصر القائمة</h2>
                <button class="close-modal">&times;</button>
            </div>
            <form id="item-editor-form">
                <div class="form-section">
                    <div class="form-section-title"><span class="en">English Information</span><span class="ar">معلومات باللغة الإنجليزية</span></div>
                    <div class="input-group">
                        <label for="item-name-en"><span class="en">Name (English)</span><span class="ar">الاسم (بالإنجليزية)</span></label>
                        <input type="text" id="item-name-en" required>
                    </div>

                    <div class="input-group">
                        <label for="item-desc-en"><span class="en">Description (English)</span><span class="ar">الوصف (بالإنجليزية)</span></label>
                        <textarea id="item-desc-en" required></textarea>
                    </div>
                </div>

                <div class="form-section">
                    <div class="form-section-title"><span class="en">Arabic Information</span><span class="ar">معلومات باللغة العربية</span></div>
                    <div class="input-group">
                        <label for="item-name-ar"><span class="en">Name (Arabic)</span><span class="ar">الاسم (بالعربية)</span></label>
                        <input type="text" id="item-name-ar" required>
                    </div>

                    <div class="input-group">
                        <label for="item-desc-ar"><span class="en">Description (Arabic)</span><span class="ar">الوصف (بالعربية)</span></label>
                        <textarea id="item-desc-ar" required></textarea>
                    </div>
                </div>

                <div class="form-section">
                    <div class="form-section-title"><span class="en">Price & Image</span><span class="ar">السعر والصورة</span></div>
                    <div class="input-group">
                        <label for="item-price"><span class="en">Price (SAR)</span><span class="ar">السعر (ريال)</span></label>
                        <input type="number" id="item-price" required>
                    </div>

                    <div class="input-group">
                        <label><span class="en">Item Image</span><span class="ar">صورة العنصر</span></label>
                        <div class="image-upload-section">
                            <div class="file-input-container">
                                <div class="file-input-trigger">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <span class="en">Click or drag to upload image</span>
                                    <span class="ar">انقر أو اسحب لتحميل الصورة</span>
                                </div>
                                <input type="file" id="item-image-file" accept="image/*">
                            </div>
                            <div class="file-name" id="item-file-name">
                                <span class="en">No file selected</span>
                                <span class="ar">لم يتم اختيار ملف</span>
                            </div>
                        </div>
                    </div>

                    <div class="image-preview" id="item-image-preview">
                        <img id="item-preview-img" src="" alt="Preview">
                    </div>

                    <input type="hidden" id="item-image-data">
                </div>

                <input type="hidden" id="item-id">
                <button type="submit" class="save-item-btn"><i class="fas fa-save"></i> <span class="en">Save Item</span><span class="ar">حفظ العنصر</span></button>
            </form>
        </div>
    </div>

    <script src="admin-dashboard.js"></script>
</body>
</html>





