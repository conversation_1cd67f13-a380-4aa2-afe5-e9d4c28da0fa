document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in
    if (!sessionStorage.getItem('adminLoggedIn')) {
        window.location.href = 'login.html';
    }
    
    // Set default language
    let currentLang = localStorage.getItem('adminLang') || 'ar';
    document.documentElement.lang = currentLang;
    
    // Update active language button
    document.getElementById('en-btn').classList.remove('active');
    document.getElementById('ar-btn').classList.remove('active');
    document.getElementById(`${currentLang}-btn`).classList.add('active');
    
    // Tab switching functionality
    const tabLinks = document.querySelectorAll('.admin-nav li');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabLinks.forEach(link => {
        link.addEventListener('click', function() {
            // Remove active class from all tabs
            tabLinks.forEach(tab => tab.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Add active class to clicked tab
            this.classList.add('active');
            
            // Show corresponding content
            const tabId = this.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });
    
    // Logout functionality
    document.getElementById('logout-btn').addEventListener('click', function() {
        sessionStorage.removeItem('adminLoggedIn');
        window.location.href = 'login.html';
    });
    
    // Load menu items for selected category
    const categorySelect = document.getElementById('category-select');
    categorySelect.addEventListener('change', loadMenuItems);
    
    // Initial load of menu items
    loadMenuItems();
    
    // Add new item button
    document.querySelector('.add-item-btn').addEventListener('click', function() {
        openItemEditor();
    });
    
    // Save changes button
    document.querySelector('.save-changes-btn').addEventListener('click', saveMenuChanges);
    
    // Modal close button
    document.querySelector('.close-modal').addEventListener('click', function() {
        document.getElementById('item-editor-modal').style.display = 'none';
    });
    
    // Item editor form
    document.getElementById('item-editor-form').addEventListener('submit', saveMenuItem);
    
    // Item image file input change
    document.getElementById('item-image-file').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const fileName = file.name;
            document.getElementById('item-file-name').innerHTML = `
                <span class="en">${fileName}</span>
                <span class="ar">${fileName}</span>
            `;
            
            const reader = new FileReader();
            reader.onload = function(event) {
                const imageData = event.target.result;
                document.getElementById('item-image-data').value = imageData;
                
                const preview = document.getElementById('item-image-preview');
                const previewImg = document.getElementById('item-preview-img');
                previewImg.src = imageData;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            document.getElementById('item-file-name').innerHTML = `
                <span class="en">No file selected</span>
                <span class="ar">لم يتم اختيار ملف</span>
            `;
            document.getElementById('item-image-preview').style.display = 'none';
        }
    });
    
    // Settings form
    document.getElementById('settings-form').addEventListener('submit', saveSettings);
    
    // Load settings
    loadSettings();
    
    // Update category select based on current language
    updateCategorySelect();
});

// Language switcher function
function switchLanguage(lang) {
    document.documentElement.lang = lang;
    localStorage.setItem('adminLang', lang);
    
    // Update active button
    document.getElementById('en-btn').classList.remove('active');
    document.getElementById('ar-btn').classList.remove('active');
    document.getElementById(`${lang}-btn`).classList.add('active');
    
    // Update category select
    updateCategorySelect();
}

// Update the category select dropdown based on language
function updateCategorySelect() {
    const categorySelect = document.getElementById('category-select');
    const currentLang = document.documentElement.lang;
    const currentValue = categorySelect.value;
    
    // Hide all language-specific options and show only the current language
    const options = categorySelect.querySelectorAll('option');
    options.forEach(option => {
        if (option.classList.contains('en') || option.classList.contains('ar')) {
            option.style.display = 'none';
        }
        if (option.classList.contains(currentLang)) {
            option.style.display = '';
        }
    });
}

// Function to show success message
function showSuccessMessage(message) {
    const successMessage = document.getElementById('success-message');
    successMessage.textContent = message;
    successMessage.classList.add('show');
    
    setTimeout(() => {
        successMessage.classList.remove('show');
    }, 5000);
}

// Function to load menu items for selected category
function loadMenuItems() {
    const category = document.getElementById('category-select').value;
    const menuItemsContainer = document.querySelector('.menu-items-editor');
    
    // Clear current items
    menuItemsContainer.innerHTML = '';
    
    // Load items from localStorage or use default items
    let menuItems = JSON.parse(localStorage.getItem('menuItems')) || {};
    if (!Array.isArray(menuItems[category])) {
        menuItems[category] = [];
    }
    
    // Display items
    if (menuItems[category].length === 0) {
        menuItemsContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-utensils"></i>
                <p class="en">No items in this category yet. Click "Add New Item" to create one.</p>
                <p class="ar">لا توجد عناصر في هذه الفئة حتى الآن. انقر على "إضافة عنصر جديد" لإنشاء واحد.</p>
            </div>
        `;
    } else {
        menuItems[category].forEach((item, index) => {
            const itemCard = document.createElement('div');
            itemCard.className = 'menu-item-card';
            itemCard.innerHTML = `
                <div class="menu-item-image">
                    <img src="${item.image && item.image.trim() ? item.image : '../images/default-food.jpg'}" alt="${item.nameEn || 'Menu item'}">
                </div>
                <div class="menu-item-info">
                    <h3 class="en">${item.nameEn || ''}</h3>
                    <h3 class="ar">${item.nameAr || ''}</h3>
                    <p class="en">${item.descEn || ''}</p>
                    <p class="ar">${item.descAr || ''}</p>
                    <p>${item.price || 0} SAR</p>
                </div>
                <div class="menu-item-actions">
                    <button class="edit-btn" data-index="${index}"><i class="fas fa-edit"></i></button>
                    <button class="delete-btn" data-index="${index}"><i class="fas fa-trash"></i></button>
                </div>
            `;
            menuItemsContainer.appendChild(itemCard);
        });
    }
    
    // Add event listeners to edit and delete buttons
    document.querySelectorAll('.edit-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const index = parseInt(this.getAttribute('data-index'));
            openItemEditor(category, index);
        });
    });
    
    document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const index = parseInt(this.getAttribute('data-index'));
            deleteMenuItem(category, index);
        });
    });
}

// Function to open item editor modal
function openItemEditor(category, index) {
    const modal = document.getElementById('item-editor-modal');
    const form = document.getElementById('item-editor-form');
    
    // Clear form
    form.reset();
    document.getElementById('item-image-preview').style.display = 'none';
    document.getElementById('item-image-data').value = '';
    document.getElementById('item-file-name').innerHTML = `
        <span class="en">No file selected</span>
        <span class="ar">لم يتم اختيار ملف</span>
    `;
    
    // Set modal title based on whether we're adding or editing
    if (category && index !== undefined) {
        document.getElementById('modal-title').textContent = 'Edit Menu Item';
        document.getElementById('modal-title-ar').textContent = 'تعديل عنصر القائمة';
    } else {
        document.getElementById('modal-title').textContent = 'Add New Menu Item';
        document.getElementById('modal-title-ar').textContent = 'إضافة عنصر جديد';
    }
    
    // If editing existing item
    if (category && index !== undefined) {
        const menuItems = JSON.parse(localStorage.getItem('menuItems')) || {};
        if (menuItems[category] && menuItems[category][index]) {
            const item = menuItems[category][index];
            
            document.getElementById('item-name-en').value = item.nameEn || '';
            document.getElementById('item-name-ar').value = item.nameAr || '';
            document.getElementById('item-desc-en').value = item.descEn || '';
            document.getElementById('item-desc-ar').value = item.descAr || '';
            document.getElementById('item-price').value = item.price || '';
            
            // Show image preview if available
            if (item.image) {
                const preview = document.getElementById('item-image-preview');
                const previewImg = document.getElementById('item-preview-img');
                previewImg.src = item.image;
                preview.style.display = 'block';
                document.getElementById('item-image-data').value = item.image;
                
                // Add remove image button if it doesn't exist
                if (!preview.querySelector('.remove-image-btn')) {
                    const removeBtn = document.createElement('button');
                    removeBtn.type = 'button';
                    removeBtn.className = 'remove-image-btn';
                    removeBtn.innerHTML = '<i class="fas fa-trash"></i> Remove Image';
                    removeBtn.onclick = function() {
                        document.getElementById('item-image-data').value = '';
                        document.getElementById('item-image-preview').style.display = 'none';
                        this.remove();
                    };
                    preview.appendChild(removeBtn);
                }
            }
        }
    }
    
    // Set the item ID for form submission
    document.getElementById('item-id').value = JSON.stringify({
        category: category || document.getElementById('category-select').value,
        index: (index !== undefined) ? index : -1
    });
    
    // Show modal
    modal.style.display = 'flex';
}

// Function to save menu item
function saveMenuItem(e) {
    e.preventDefault();
    
    const idData = JSON.parse(document.getElementById('item-id').value);
    const category = idData.category;
    const index = parseInt(idData.index);
    
    // Get image data - either from the file input or keep existing
    let imageData = document.getElementById('item-image-data').value;
    
    const item = {
        nameEn: document.getElementById('item-name-en').value,
        nameAr: document.getElementById('item-name-ar').value,
        descEn: document.getElementById('item-desc-en').value,
        descAr: document.getElementById('item-desc-ar').value,
        price: document.getElementById('item-price').value,
        image: imageData || '../images/default-food.jpg'
    };
    
    // Get existing menu items
    let menuItems = JSON.parse(localStorage.getItem('menuItems')) || {};
    
    // Ensure the category exists
    if (!menuItems[category]) {
        menuItems[category] = [];
    }
    
    if (index === -1) {
        // Add new item
        menuItems[category].push(item);
        showSuccessMessage('New menu item added successfully! Changes will appear on the main website.');
    } else {
        // Update existing item
        menuItems[category][index] = item;
        showSuccessMessage('Menu item updated successfully! Changes will appear on the main website.');
    }
    
    // Save to localStorage
    localStorage.setItem('menuItems', JSON.stringify(menuItems));
    
    // Close modal and reload items
    document.getElementById('item-editor-modal').style.display = 'none';
    loadMenuItems();
}

// Function to delete menu item
function deleteMenuItem(category, index) {
    if (confirm('Are you sure you want to delete this item?')) {
        let menuItems = JSON.parse(localStorage.getItem('menuItems')) || {};
        menuItems[category].splice(index, 1);
        localStorage.setItem('menuItems', JSON.stringify(menuItems));
        showSuccessMessage('Menu item deleted successfully!');
        loadMenuItems();
    }
}

// Function to save all menu changes
function saveMenuChanges() {
    showSuccessMessage('All changes have been saved!');
}

// Function to load settings
function loadSettings() {
    const settings = JSON.parse(localStorage.getItem('settings')) || {};
    
    if (settings.restaurantNameEn) {
        document.getElementById('restaurant-name-en').value = settings.restaurantNameEn;
    }
    
    if (settings.restaurantNameAr) {
        document.getElementById('restaurant-name-ar').value = settings.restaurantNameAr;
    }
    
    if (settings.contactPhone) {
        document.getElementById('contact-phone').value = settings.contactPhone;
    }
    
    if (settings.contactAddressEn) {
        document.getElementById('contact-address-en').value = settings.contactAddressEn;
    }
    
    if (settings.contactAddressAr) {
        document.getElementById('contact-address-ar').value = settings.contactAddressAr;
    }
}

// Function to save settings
function saveSettings(e) {
    e.preventDefault();
    
    const settings = {
        restaurantNameEn: document.getElementById('restaurant-name-en').value,
        restaurantNameAr: document.getElementById('restaurant-name-ar').value,
        contactPhone: document.getElementById('contact-phone').value,
        contactAddressEn: document.getElementById('contact-address-en').value,
        contactAddressAr: document.getElementById('contact-address-ar').value
    };
    
    localStorage.setItem('settings', JSON.stringify(settings));
    showSuccessMessage('Settings saved successfully! Changes will appear on the main website.');
}





