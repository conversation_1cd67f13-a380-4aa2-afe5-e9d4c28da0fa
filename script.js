// Set default language
let currentLang = localStorage.getItem('lang') || 'ar';
document.documentElement.lang = currentLang;

// Update active button
document.getElementById('en-btn').classList.remove('active');
document.getElementById('ar-btn').classList.remove('active');
document.getElementById(`${currentLang}-btn`).classList.add('active');

// Language switcher
function switchLanguage(lang) {
    currentLang = lang;
    document.documentElement.lang = lang;
    localStorage.setItem('lang', lang);

    // Update active button
    document.getElementById('en-btn').classList.remove('active');
    document.getElementById('ar-btn').classList.remove('active');
    document.getElementById(`${lang}-btn`).classList.add('active');
}

// Load settings from localStorage
function loadSettings() {
    const settings = JSON.parse(localStorage.getItem('settings'));

    if (settings) {
        // Update restaurant name
        if (settings.restaurantNameEn) {
            document.querySelectorAll('.logo .en').forEach(el => {
                el.textContent = settings.restaurantNameEn;
            });
        }

        if (settings.restaurantNameAr) {
            document.querySelectorAll('.logo .ar').forEach(el => {
                el.textContent = settings.restaurantNameAr;
            });
        }

        // Update contact information
        if (settings.contactPhone) {
            document.querySelectorAll('#contact .fa-phone + p').forEach(el => {
                el.textContent = settings.contactPhone;
            });
        }

        if (settings.contactAddressEn) {
            document.querySelectorAll('#contact .fa-map-marker-alt + p.en').forEach(el => {
                el.textContent = settings.contactAddressEn;
            });
        }

        if (settings.contactAddressAr) {
            document.querySelectorAll('#contact .fa-map-marker-alt + p.ar').forEach(el => {
                el.textContent = settings.contactAddressAr;
            });
        }
    }
}

// Load menu items from localStorage
function loadMenuItems() {
    const menuItems = JSON.parse(localStorage.getItem('menuItems'));

    if (menuItems) {
        // Loop through each category
        for (const category in menuItems) {
            const categoryContainer = document.querySelector(`.menu-items.${category}`);

            if (categoryContainer && menuItems[category] && menuItems[category].length > 0) {
                // Add items from localStorage
                menuItems[category].forEach(item => {
                    if (item && (item.nameEn || item.nameAr)) {  // Only add valid items
                        // Fix image path if it's from admin
                        let imagePath = item.image;
                        if (imagePath && imagePath.startsWith('../')) {
                            imagePath = imagePath.substring(3); // Remove '../' prefix
                        }

                        // Check if this item already exists by name
                        const existingItem = Array.from(categoryContainer.querySelectorAll('.menu-item')).find(el => {
                            const nameEl = el.querySelector('.menu-item-info h3.en');
                            return nameEl && nameEl.textContent === item.nameEn;
                        });

                        // Only add if it doesn't exist
                        if (!existingItem) {
                            const menuItemElement = document.createElement('div');
                            menuItemElement.className = 'menu-item';

                            menuItemElement.innerHTML = `
                                <div class="menu-item-info">
                                    <h3 class="en">${item.nameEn || ''}</h3>
                                    <h3 class="ar">${item.nameAr || ''}</h3>
                                    <p class="en">${item.descEn || ''}</p>
                                    <p class="ar">${item.descAr || ''}</p>
                                    <span class="price">${item.price || 0} SAR</span>
                                </div>
                                <div class="menu-item-image" style="background-image: url('${imagePath || 'images/hummus.JPG'}')"></div>
                            `;
                            categoryContainer.appendChild(menuItemElement);
                        }
                    }
                });
            }
        }
    }
}

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();

        const targetId = this.getAttribute('href');
        const targetElement = document.querySelector(targetId);

        if (targetElement) {
            window.scrollTo({
                top: targetElement.offsetTop - 70, // Offset for header
                behavior: 'smooth'
            });
        }
    });
});

// Menu tabs functionality
function showCategory(category) {
    // Hide all menu items
    const menuItems = document.querySelectorAll('.menu-items');
    menuItems.forEach(item => {
        item.style.display = 'none';
    });

    // Show selected category
    document.querySelector(`.${category}`).style.display = 'grid';

    // Update active tab
    const tabs = document.querySelectorAll('.menu-tab');
    tabs.forEach(tab => {
        tab.classList.remove('active');
    });

    event.currentTarget.classList.add('active');
}

// Responsive navigation
window.addEventListener('resize', function() {
    // Reset slider position on window resize
    currentIndex = 0;
    slider.style.transform = 'translateX(0)';
});

// Load settings and menu items when the page loads
document.addEventListener('DOMContentLoaded', function() {
    loadSettings();
    loadMenuItems();
});













